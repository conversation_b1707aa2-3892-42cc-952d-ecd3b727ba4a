# Phase 5 Implementation Plan — Readiness, QA, and Rollout

This phase closes the loop on the agent approvals program by hardening streaming behaviour, completing the feedback workflow, polishing the renderer UX, and delivering the test/documentation assets needed for GA. All work must honour `TYPESCRIPT.md` (no widened types, branded IDs, exhaustive unions) and `TESTING.md` (behaviour-first assertions, ≥2 per test, low mock count).

---

## Objectives
- Guarantee that cancellations, streaming state transitions, and auto-apply limits behave consistently across main/renderer.
- Ship the adaptive approval actions (approve with edits, request changes) and richer preview surfaces promised in the master plan.
- Ensure reviewer feedback feeds back into the conversation loop and audit trail.
- Raise confidence to GA: broaden automated coverage, document the flow, and define the rollout steps for enabling `AGENT_APPROVAL_V2`.

### Success Criteria
- Cancelling an in-flight preview stops renderer jobs, updates status+timestamps, and emits timeline updates within 1s.
- Approval cards surface adaptive actions, diff/file chips, and support edit payloads without regressions for existing tools.
- Rejection or approval feedback produces a persisted note and a synthetic chat message visible to the agent next turn.
- Jest + Playwright suites cover main-process lifecycle, renderer controls, and legacy bypass regressions; CI passes without flaky skips.
- Documentation (`AGENTS.md`, `RELEASE.md`, QA checklist) reflects the new consent flow and rollout plan.
- `AGENT_APPROVAL_V2` can be enabled by default with a clear fallback toggle.

---

## Current Gaps (Phase 1–4 vs Master Plan)
- `ApprovalsService.cancelPreview` only updates the database; it never signals the `PreviewController`/renderer to stop jobs, so streaming work keeps running. (`src/main/agent/approvals-service.ts:352`)
- Streaming state is inferred once from stored detail; there is no pipeline to update preview rows as status changes, so UI buttons can get stuck disabled.
- Approval cards expose only approve/reject; there is no "approve with edits" UI or tool-specific button labelling (`src/components/agent-approvals/agent-approval-card.tsx` + `button-config.ts`).
- File previews lack action badges / warnings for create/delete/move, and the list is not virtualised (risking large DOM payloads) with bypass toggle hidden (`src/components/agent-approvals/agent-approval-list.tsx:92`).
- Reviewer comments are persisted but never injected into chat threads, limiting follow-up context for the agent.
- Auto-approved items never surface in a digest; audit visibility depends on digging through the timeline only.
- No end-to-end test validates approvals, and GA docs/runbooks have not been updated for the new flow.

---

## Workstreams & Implementation Steps

### 1. Streaming & Cancellation Hardening
1. **Preview Controller bridge**: teach `ApprovalsService.cancelPreview` to call a new dependency (`PreviewController.cancel(previewId)`), wiring it through service construction (`main.ts`) so renderer jobs are actually aborted. Ensure tool-specific clean-up runs even when DB writes fail.
2. **Streaming status updates**: subscribe to `PreviewController` status events and persist streaming metadata (`state`, `finishedAt`, partial output hashes) back into `agent_tool_previews`. Emit `agent:approval:update` with refreshed preview detail so `deriveStreamingState` reflects transitions.
3. **Auto-apply caps**: persist cap hits per session in DB (e.g., `agent.approvals.autoCapUsage`) so restarts don't drop counts; expose telemetry for rejected auto-applies.
4. **Acceptance criteria**: cancel stops renderer output within 1s; timeline shows `FAILED (cancelled)` with timestamps; auto-apply cap persists across restarts. Tests cover cancellation + streaming events.

### 2. Adaptive Approval Actions & Preview Polish
1. **Approve with edits UI**: add an inline diff editor modal (reuse existing diff utils) that allows editing text before calling `agent:approval:apply-with-content`. Validate payloads with Zod before sending.
2. **Request changes / mark applied**: extend `button-config.ts` to generate tool-specific primary/secondary actions (file write → “Apply write”; edit diff → “Apply patch”; add tertiary “Request changes” that stores feedback and leaves item pending/failed per master plan).
3. **Rich file chips**: enhance `AgentApprovalCard` to show action badges (create/delete/move) and warnings for out-of-workspace paths using `validateAndResolvePath` metadata.
4. **Virtualised list & bypass control**: swap the map loop for `react-window` (only when count > N). Restore the bypass toggle in the empty state and header.
5. **Auto-approved digest**: render a collapsible "Auto-approved this session" tray pulling from export data; include badge counts and quick links to timeline anchors.
6. **Acceptance criteria**: editing payload updates DB + timeline; virtualization kicks in >=25 items; auto-approved tray lists reason + allow manual review.

### 3. Feedback Loop & Audit Trail
1. **Synthetic chat messages**: when `rejectApproval` or `applyApproval` receives `feedbackText`, write a system message (`role: "system"`, type `approval_feedback`) into the chat thread via `chat-storage` so the agent sees reviewer notes next turn.
2. **Resolved-by attribution**: pass a `resolvedBy` string from IPC (e.g., renderer user display name or `"user"`) into `applyApproval`; reserve `"system"` only for auto-approve flows.
3. **Export metadata**: include feedback entries and `resolvedBy` in `agent:export-session` payload; add hashes for before/after files when available to improve audit fidelity.
4. **Acceptance criteria**: reviewer notes appear in the message list, export JSON lists feedback+hashes, and audit logs capture resolver identity.

### 4. Performance, Telemetry & Ops
1. Instrument key lifecycle events (`preview_recorded`, `approval_applied`, `approval_cancelled`) with duration + session IDs, feeding existing logging/metrics pipeline.
2. Add health checks ensuring approval tables are vacuumed when exceeding thresholds (new DB maintenance cron in `main.ts`).
3. Implement a diagnostic command (`npm run approvals:doctor`) to output flag state, auto rule counts, and pending approvals for support.

### 5. Testing & Automation
1. **Main-process Jest**: add suites covering cancellation bridge, streaming update persistence, auto-cap exhaustion, synthetic chat messages, and export payloads.
2. **Renderer RTL**: test approve-with-edits modal, adaptive buttons, virtualization threshold, auto-approved tray, and bypass toggle wiring.
3. **End-to-end**: author a Playwright scenario that runs a diff tool call, verifies pending approval, applies with edits, confirms file change + timeline update, then cancels a terminal preview.
4. **Legacy regression**: ensure legacy `AgentToolCalls` flow still works with `AGENT_APPROVAL_V2` off (unit + Playwright coverage).
5. **CI gating**: update scripts to run new tests on `npm run verify-build`; enforce `TESTING.md` rules via `test-quality-guard` additions for new files.

### 6. Documentation, QA & Rollout
1. **Docs**: update `AGENTS.md` (Phase 5 section), `RELEASE.md` (migration + rollout instructions), and create `docs/approvals-qa-checklist.md` enumerating manual tests (diff, terminal, auto rules, export, bypass).
2. **Flag strategy**: introduce config that defaults `AGENT_APPROVAL_V2` to true in dev/staging while keeping prod opt-in; document rollback.
3. **Training assets**: record animated GIF or short Loom showing approve with edits + timeline navigation for support teams.
4. **Acceptance criteria**: docs merged, QA checklist reviewed, rollout plan signed off, sample changelog prepared.

---

## Estimated Effort & Dependencies
- Streaming hardening: Large (cross-process wiring, DB migrations) — depends on `PreviewController` & `RendererPreviewProxy` APIs.
- UI polish + adaptive actions: Medium-Large — depends on new streaming events and existing diff utilities.
- Feedback loop integration: Medium — depends on chat storage schema; ensure migrations preserve data.
- Testing + automation: Medium — requires Playwright env and CI updates.
- Docs & rollout: Small-Medium — no external deps besides prior workstreams.

---

## Testing & Validation Summary
- Unit/Jest: main-process (approvals service, export), renderer hooks/components, DB migrations.
- Integration: Playwright scenario + `npm run verify-build` pipeline.
- Manual QA: checklist covering approvals on/off, auto rules, cancellation, export, bypass toggle, feedback loop.
- Monitoring: verify telemetry events in staging before GA rollout.

---

## Acceptance Checklist
- [ ] Cancelling previews halts renderer jobs, persists status/autoReason, and updates UI in under 1s.
- [ ] Approval cards offer adaptive buttons (approve with edits, request changes) and rich preview context for files/terminal.
- [ ] Reviewer feedback writes to chat threads and exports, with accurate `resolvedBy` attribution.
- [ ] Auto-approve caps persist per session and auto-approved items surface in UI digest + timeline.
- [ ] Jest + Playwright suites cover new flows and run in CI without flakes; legacy bypass flow verified.
- [ ] Documentation, QA checklist, and rollout notes updated; flag strategy defined for GA.

